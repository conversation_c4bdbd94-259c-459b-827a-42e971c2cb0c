aenum==3.1.15 ; python_version >= "3.11" and python_version < "4.0"
aiohttp==3.9.5 ; python_version >= "3.11" and python_version < "4.0"
aiosignal==1.3.2 ; python_version >= "3.11" and python_version < "4.0"
aniso8601==10.0.0 ; python_version >= "3.11" and python_version < "4.0"
annotated-types==0.7.0 ; python_version >= "3.11" and python_version < "4.0"
anyio==4.9.0 ; python_version >= "3.11" and python_version < "4.0"
attrs==25.3.0 ; python_version >= "3.11" and python_version < "4.0"
blinker==1.9.0 ; python_version >= "3.11" and python_version < "4.0"
certifi==2025.1.31 ; python_version >= "3.11" and python_version < "4.0"
charset-normalizer==3.4.1 ; python_version >= "3.11" and python_version < "4.0"
click==8.1.8 ; python_version >= "3.11" and python_version < "4.0"
colorama==0.4.6 ; python_version >= "3.11" and python_version < "4.0" and platform_system == "Windows"
fastapi==0.109.2 ; python_version >= "3.11" and python_version < "4.0"
flask-restx==1.3.0 ; python_version >= "3.11" and python_version < "4.0"
flask==3.0.3 ; python_version >= "3.11" and python_version < "4.0"
frozenlist==1.5.0 ; python_version >= "3.11" and python_version < "4.0"
h11==0.14.0 ; python_version >= "3.11" and python_version < "4.0"
idna==3.10 ; python_version >= "3.11" and python_version < "4.0"
ifaddr==0.2.0 ; python_version >= "3.11" and python_version < "4.0"
importlib-resources==6.5.2 ; python_version >= "3.11" and python_version < "4.0"
influxdb-client==1.36.1 ; python_version >= "3.11" and python_version < "4.0"
itsdangerous==2.2.0 ; python_version >= "3.11" and python_version < "4.0"
jinja2==3.1.6 ; python_version >= "3.11" and python_version < "4.0"
jsonschema-specifications==2024.10.1 ; python_version >= "3.11" and python_version < "4.0"
jsonschema==4.23.0 ; python_version >= "3.11" and python_version < "4.0"
markupsafe==3.0.2 ; python_version >= "3.11" and python_version < "4.0"
multidict==6.2.0 ; python_version >= "3.11" and python_version < "4.0"
netifaces==0.11.0 ; python_version >= "3.11" and python_version < "4.0"
prompt-toolkit==3.0.36 ; python_version >= "3.11" and python_version < "4.0"
propcache==0.3.0 ; python_version >= "3.11" and python_version < "4.0"
psutil==5.9.8 ; python_version >= "3.11" and python_version < "4.0"
pydantic-core==2.27.2 ; python_version >= "3.11" and python_version < "4.0"
pydantic==2.10.6 ; python_version >= "3.11" and python_version < "4.0"
pymodbus==3.8.6 ; python_version >= "3.11" and python_version < "4.0"
pymodbustcp==0.2.2 ; python_version >= "3.11" and python_version < "4.0"
python-dateutil==2.8.2 ; python_version >= "3.11" and python_version < "4.0"
pytz==2025.1 ; python_version >= "3.11" and python_version < "4.0"
pyyaml==6.0.2 ; python_version >= "3.11" and python_version < "4.0"
questionary==2.0.1 ; python_version >= "3.11" and python_version < "4.0"
reactivex==4.0.4 ; python_version >= "3.11" and python_version < "4.0"
referencing==0.36.2 ; python_version >= "3.11" and python_version < "4.0"
requests==2.31.0 ; python_version >= "3.11" and python_version < "4.0"
rpds-py==0.23.1 ; python_version >= "3.11" and python_version < "4.0"
setuptools==76.1.0 ; python_version >= "3.11" and python_version < "4.0"
six==1.17.0 ; python_version >= "3.11" and python_version < "4.0"
sniffio==1.3.1 ; python_version >= "3.11" and python_version < "4.0"
starlette==0.36.3 ; python_version >= "3.11" and python_version < "4.0"
typing-extensions==4.12.2 ; python_version >= "3.11" and python_version < "4.0"
urllib3==2.3.0 ; python_version >= "3.11" and python_version < "4.0"
uvicorn==0.27.1 ; python_version >= "3.11" and python_version < "4.0"
wallbox==0.8.0 ; python_version >= "3.11" and python_version < "4.0"
wcwidth==0.2.13 ; python_version >= "3.11" and python_version < "4.0"
werkzeug==3.1.3 ; python_version >= "3.11" and python_version < "4.0"
yarl==1.18.3 ; python_version >= "3.11" and python_version < "4.0"
zeroconf==0.131.0 ; python_version >= "3.11" and python_version < "4.0"
