/* Base layout */
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

h1 {
    color: #333;
}

/* Navigation */
.nav-links {
    margin-bottom: 20px;
}

.nav-links a {
    color: #0066cc;
    text-decoration: none;
    margin-right: 15px;
}

.nav-links a:hover {
    text-decoration: underline;
}

/* Forms and sections */
.form-group {
    margin-bottom: 15px;
}

.config-section,
.schedule-form {
    background: #f5f5f5;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 5px;
}

.scheduled-events {
    margin-top: 30px;
}

.status-panel {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    margin: 20px 0;
}

.status-item {
    margin: 10px 0;
}

.status-item strong {
    color: #495057;
}

.status-item .separator {
    margin: 0 20px;
    color: #dee2e6;
}

.status-info {
    display: inline-block;
    margin-right: 20px;
}

/* Responsive design */
@media (max-width: 768px) {
    .legend-grid {
        grid-template-columns: 1fr;
    }

    .legend-toggle {
        margin-left: 0;
        width: 100%;
    }
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

input[type="text"],
input[type="password"],
input[type="number"],
input[type="datetime-local"] {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
}

input[type="checkbox"] {
    margin-right: 8px;
}

.checkbox-label {
    display: inline-block;
}

select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

button {
    background: #4CAF50;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

/* Remove the global button hover style */
/* button:hover {
    background: #45a049;
} */

/* Add specific styles for form and action buttons */
.schedule-form button:hover,
.config-form button:hover,
.modal-buttons button:hover {
    background: #45a049;
}

/* Keep other button styles as needed */

/* Flash messages */
.flash-messages {
    margin: 20px 0;
}

.flash-message {
    padding: 10px;
    margin-bottom: 20px;
    border-radius: 4px;
}

.flash-success {
    background: #dff0d8;
    border: 1px solid #d6e9c6;
    color: #3c763d;
}

.flash-error {
    background: #f2dede;
    border: 1px solid #ebccd1;
    color: #a94442;
}

/* SVG button container styles */
.svg-button-container button {
    height: 48px;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    cursor: pointer;
}

.svg-button-container button:hover {
    background-color: #e9ecef;
    transform: scale(1.05);
}

.svg-button-container button:active {
    transform: scale(0.95);
}

/* Remove the global button styles for this container */
.svg-button-container button {
    background: inherit;
    /* Override the global green background */
    color: inherit;
    /* Override the global white text */
}

/* Specific adjustments for text-only buttons */
.svg-button-container button:not(.svg-button) {
    padding: 4px 12px;
    /* Slightly more padding for text buttons */
}

/* Remove spinner buttons from number inputs */
.no-spinner::-webkit-inner-spin-button,
.no-spinner::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.no-spinner {
    -moz-appearance: textfield;
}