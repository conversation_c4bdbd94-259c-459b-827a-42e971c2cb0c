name: Tests

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    - name: Install Poetry
      run: |
        curl -sSL https://install.python-poetry.org | python3 -
    - name: Install dependencies
      run: |
        poetry install --with dev
    - name: Run tests
      run: |
        poetry run pytest