[tool.poetry]
name = "evse-controller"
version = "0.3.0"
description = "EVSE Controller with Wallbox Quasar support"
authors = ["<PERSON><PERSON> <<EMAIL>>"]
packages = [
    { include = "evse_controller", from = "src" },
    { include = "evse_discovery", from = "src" }
]

[tool.poetry.dependencies]
psutil = "^5.9.0"
python = "^3.11"
requests = "~2.31.0"
pyModbusTCP = "~0.2.0"
wallbox = "~0.8.0"
flask = "~3.0.0"
flask-restx = "~1.3.0"
influxdb-client = "~1.36.0"
questionary = "~2.0.1"
pyyaml = "~6.0.1"
python-dateutil = "~2.8.2"
typing-extensions = ">=4.12.2"
# Additional dependencies for evse_discovery
fastapi = "^0.109.0"
uvicorn = "^0.27.0"
zeroconf = "^0.131.0"
netifaces = "^0.11.0"
pymodbus = "^3.6.3"
aiohttp = "~3.9.0"

[tool.poetry.group.dev.dependencies]
flake8 = "~7.0.0"
pytest = "^7.4.4"
black = "^24.1.0"
isort = "^5.13.2"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
